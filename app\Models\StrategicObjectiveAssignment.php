<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StrategicObjectiveAssignment extends Model
{

    protected $fillable = [
        'strategic_objective_id',
        'organization_unit_id',
        'weight',
        'is_active',
    ];

    public function strategicObjective()
    {
        return $this->belongsTo(StrategicObjective::class);
    }

    public function organizationUnit()
    {
        return $this->belongsTo(OrganizationUnit::class);
    }
}
