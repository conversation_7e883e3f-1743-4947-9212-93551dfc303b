<?php

namespace App\Filament\Resources\PerspectiveResource\Pages;

use App\Filament\Resources\PerspectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPerspectives extends ListRecords
{
    protected static string $resource = PerspectiveResource::class;

    public function getTitle(): string
    {
        return __('resource.perspective_resource.label');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
