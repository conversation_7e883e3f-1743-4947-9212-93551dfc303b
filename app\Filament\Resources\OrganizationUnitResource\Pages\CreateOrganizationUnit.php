<?php

namespace App\Filament\Resources\OrganizationUnitResource\Pages;

use App\Filament\Resources\OrganizationUnitResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateOrganizationUnit extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = OrganizationUnitResource::class;
    public function getTitle(): string
    {
        return __('resource.organization_unit_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
