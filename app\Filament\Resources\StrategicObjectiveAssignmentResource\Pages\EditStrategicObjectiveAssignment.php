<?php

namespace App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;

use App\Filament\Resources\StrategicObjectiveAssignmentResource;
use App\Models\StrategicObjectiveAssignment;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditStrategicObjectiveAssignment extends EditRecord
{
    protected static string $resource = StrategicObjectiveAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Get the current user's child organization units
        $user = Auth::user();
        $childUnits = collect();

        if ($user && $user->employee && $user->employee->organizationUnit) {
            $childUnits = $user->employee->organizationUnit->children()->get();
        }

        // Get all existing assignments for this strategic objective and these organization units
        $existingAssignments = StrategicObjectiveAssignment::where('strategic_objective_id', $data['strategic_objective_id'])
            ->whereIn('organization_unit_id', $childUnits->pluck('id'))
            ->get()
            ->keyBy('organization_unit_id');

        // Transform the data to match the form structure
        $assignments = [];
        foreach ($childUnits as $unit) {
            $assignment = $existingAssignments->get($unit->id);
            if ($assignment) {
                $assignments[$unit->id] = [
                    'strategic_objective_id' => $assignment->strategic_objective_id,
                    'strategic_objective_weight' => $assignment->strategicObjective->weight ?? null,
                    'organization_unit_name' => $unit->name,
                    'organization_unit_id' => $unit->id,
                    'assignment_weight' => $assignment->weight,
                    'is_active' => $assignment->is_active,
                ];
            } else {
                // Create empty structure for units without assignments
                $assignments[$unit->id] = [
                    'strategic_objective_id' => null,
                    'strategic_objective_weight' => null,
                    'organization_unit_name' => $unit->name,
                    'organization_unit_id' => $unit->id,
                    'assignment_weight' => null,
                    'is_active' => true,
                ];
            }
        }

        $data['assignments'] = $assignments;
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Get the current user's child organization units
        $user = Auth::user();
        $childUnits = collect();

        if ($user && $user->employee && $user->employee->organizationUnit) {
            $childUnits = $user->employee->organizationUnit->children()->get();
        }

        if (isset($data['assignments'])) {
            // Delete existing assignments for these organization units
            StrategicObjectiveAssignment::whereIn('organization_unit_id', $childUnits->pluck('id'))
                ->delete();

            // Create new assignments
            foreach ($data['assignments'] as $unitId => $assignment) {
                if (isset($assignment['strategic_objective_id']) &&
                    isset($assignment['assignment_weight']) &&
                    !empty($assignment['strategic_objective_id']) &&
                    !empty($assignment['assignment_weight'])) {

                    StrategicObjectiveAssignment::create([
                        'strategic_objective_id' => $assignment['strategic_objective_id'],
                        'organization_unit_id' => $assignment['organization_unit_id'] ?? $unitId,
                        'weight' => $assignment['assignment_weight'],
                        'is_active' => $assignment['is_active'] ?? true,
                    ]);
                }
            }
        }

        // Return the original record data (we handle the assignments separately)
        return [
            'strategic_objective_id' => $data['assignments'][array_key_first($data['assignments'])]['strategic_objective_id'] ?? $this->record->strategic_objective_id,
            'organization_unit_id' => $this->record->organization_unit_id,
            'weight' => $this->record->weight,
            'is_active' => $this->record->is_active,
        ];
    }

    protected function afterSave(): void
    {
        // Redirect to index after successful save
        $this->redirect($this->getResource()::getUrl('index'));
    }
}
