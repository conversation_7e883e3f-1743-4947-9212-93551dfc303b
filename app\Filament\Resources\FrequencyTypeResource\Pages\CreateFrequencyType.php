<?php

namespace App\Filament\Resources\FrequencyTypeResource\Pages;

use App\Filament\Resources\FrequencyTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateFrequencyType extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = FrequencyTypeResource::class;

    public function getTitle(): string
    {
        return __('resource.frequency_type_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
