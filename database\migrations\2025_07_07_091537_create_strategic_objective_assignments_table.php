<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('strategic_objective_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('strategic_objective_id');
            $table->foreign('strategic_objective_id')->references('id')->on('strategic_objectives')->onDelete('cascade');
            $table->unsignedBigInteger('organization_unit_id');
            $table->foreign('organization_unit_id')->references('id')->on('organization_units')->onDelete('cascade');
            $table->decimal('weight', 5, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('strategic_objective_assignments');
    }
};
