<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmployeeResource\Pages;
use App\Filament\Resources\EmployeeResource\RelationManagers;
use App\Models\Employee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EmployeeResource extends Resource
{
    use Translatable;
    protected static ?string $model = Employee::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.user_management_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationBadgeTooltip(): ?string
    {
        return 'Number of employees';
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.employee_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.employee_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.employee_resource.fields.name')),
                Forms\Components\Select::make('sex')->options([
                    'male' => __('resource.employee_resource.fields.sex_male'),
                    'female' => __('resource.employee_resource.fields.sex_female'),
                ])->label(__('resource.employee_resource.fields.sex'))->placeholder(__('resource.employee_resource.fields.sex')),
                Forms\Components\DatePicker::make('job_position_date')->label(__('resource.employee_resource.fields.job_position_date')),
                Forms\Components\Select::make('job_position_id')->relationship('jobPosition', 'name')->label(__('resource.employee_resource.fields.job_position_id'))->placeholder(__('resource.employee_resource.fields.job_position_id_placeholder')),
                Forms\Components\Select::make('organization_unit_id')->relationship('organizationUnit', 'name')->label(__('resource.employee_resource.fields.organization_unit_id'))->placeholder(__('resource.employee_resource.fields.organization_unit_id_placeholder')),
                Forms\Components\Select::make('job_type_id')->relationship('jobType', 'name')->label(__('resource.employee_resource.fields.job_type_id'))->placeholder(__('resource.employee_resource.fields.job_type_id_placeholder')),
                Forms\Components\Toggle::make('is_active')->label(__('resource.employee_resource.fields.is_active')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.employee_resource.fields.name')),
                Tables\Columns\TextColumn::make('sex')->label(__('resource.employee_resource.fields.sex')),
                Tables\Columns\TextColumn::make('job_position_date')->label(__('resource.employee_resource.fields.job_position_date')),
                Tables\Columns\TextColumn::make('jobPosition.name')->label(__('resource.employee_resource.fields.job_position_id')),
                Tables\Columns\TextColumn::make('organizationUnit.name')->label(__('resource.employee_resource.fields.organization_unit_id')),
                Tables\Columns\TextColumn::make('jobType.name')->label(__('resource.employee_resource.fields.job_type_id')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployees::route('/'),
            'create' => Pages\CreateEmployee::route('/create'),
            'edit' => Pages\EditEmployee::route('/{record}/edit'),
        ];
    }
}
