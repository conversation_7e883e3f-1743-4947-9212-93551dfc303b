<?php

namespace App\Filament\Resources\KpiResource\Pages;

use App\Filament\Resources\KpiResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateKpi extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = KpiResource::class;

    public function getTitle(): string
    {
        return __('resource.kpi_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
