<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Perspective extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'description',
        'weight',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];

    public $translatable = ['name', 'description'];
}
