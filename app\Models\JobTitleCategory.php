<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class JobTitleCategory extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'code',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];

    public $translatable = ['name', 'description'];

    public function parent()
    {
        return $this->belongsTo(JobTitleCategory::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(JobTitleCategory::class, 'parent_id');
    }


}
