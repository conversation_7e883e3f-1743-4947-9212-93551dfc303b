<?php

namespace App\Filament\Resources\JobTitleCategoryResource\Pages;

use App\Filament\Resources\JobTitleCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditJobTitleCategory extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = JobTitleCategoryResource::class;
    public function getTitle(): string
    {
        return __('resource.job_title_category_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
