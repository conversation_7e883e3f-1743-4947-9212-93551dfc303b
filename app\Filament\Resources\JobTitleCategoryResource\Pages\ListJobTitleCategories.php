<?php

namespace App\Filament\Resources\JobTitleCategoryResource\Pages;

use App\Filament\Resources\JobTitleCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListJobTitleCategories extends ListRecords
{
    protected static string $resource = JobTitleCategoryResource::class;
    public function getTitle(): string
    {
        return __('resource.job_title_category_resource.label');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
