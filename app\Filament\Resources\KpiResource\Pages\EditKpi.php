<?php

namespace App\Filament\Resources\KpiResource\Pages;

use App\Filament\Resources\KpiResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKpi extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = KpiResource::class;

    public function getTitle(): string
    {
        return __('resource.kpi_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
