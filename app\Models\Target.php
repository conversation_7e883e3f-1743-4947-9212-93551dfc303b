<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Target extends Model
{
    use HasTranslations;

    protected $fillable = [
        'target',
        'description',
        'year',
        'month_id',
        'quarter_id',
        'half_year_id',
        'kpi_id',
        'user_id',
        'approved_by',
        'is_approved',
        'is_active',
    ];

    protected $casts = [
        'target' => 'array',
        'description' => 'array',
    ];

    public $translatable = ['target', 'description'];

    public function month()
    {
        return $this->belongsTo(Month::class);
    }

    public function quarter()
    {
        return $this->belongsTo(Quarter::class);
    }

    public function halfYear()
    {
        return $this->belongsTo(HalfYear::class);
    }

    public function kpi()
    {
        return $this->belongsTo(Kpi::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

}
