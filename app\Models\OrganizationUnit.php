<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class OrganizationUnit extends Model
{
     use HasTranslations;
    protected $fillable = [
        'name',
        'acronym',
        'unit_type_id',
        'parent_id',
        'is_active',
        'is_root',
        'is_last_child',
    ];

    protected $casts = [
        'name' => 'array',
        'acronym' => 'array',
    ];

    public $translatable = ['name', 'acronym'];

    public function unitType()
    {
        return $this->belongsTo(UnitType::class);
    }

    public function parent()
    {
        return $this->belongsTo(OrganizationUnit::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(OrganizationUnit::class, 'parent_id');
    }

}
