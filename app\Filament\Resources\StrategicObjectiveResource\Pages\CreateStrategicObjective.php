<?php

namespace App\Filament\Resources\StrategicObjectiveResource\Pages;

use App\Filament\Resources\StrategicObjectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateStrategicObjective extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = StrategicObjectiveResource::class;

    public function getTitle(): string
    {
        return __('resource.strategic_objective_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
