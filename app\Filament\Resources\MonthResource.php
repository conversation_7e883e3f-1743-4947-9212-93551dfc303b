<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MonthResource\Pages;
use App\Filament\Resources\MonthResource\RelationManagers;
use App\Models\Month;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MonthResource extends Resource
{
    use Translatable;
    protected static ?string $model = Month::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.month_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.month_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.month_resource.fields.name')),
                Forms\Components\Select::make('half_year_id')->relationship('halfYear', 'name')->label(__('resource.month_resource.fields.half_year_id'))->searchable()->preload(),
                Forms\Components\Select::make('quarter_id')->relationship('quarter', 'name')->label(__('resource.month_resource.fields.quarter_id'))->searchable()->preload(),
                Forms\Components\DatePicker::make('start_date')->label(__('resource.month_resource.fields.start_date')),
                Forms\Components\DatePicker::make('end_date')->label(__('resource.month_resource.fields.end_date')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.month_resource.fields.name')),
                Tables\Columns\TextColumn::make('halfYear.name')->label(__('resource.month_resource.fields.half_year_id')),
                Tables\Columns\TextColumn::make('quarter.name')->label(__('resource.month_resource.fields.quarter_id')),
                Tables\Columns\TextColumn::make('start_date')->label(__('resource.month_resource.fields.start_date')),
                Tables\Columns\TextColumn::make('end_date')->label(__('resource.month_resource.fields.end_date')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMonths::route('/'),
            'create' => Pages\CreateMonth::route('/create'),
            'edit' => Pages\EditMonth::route('/{record}/edit'),
        ];
    }
}
