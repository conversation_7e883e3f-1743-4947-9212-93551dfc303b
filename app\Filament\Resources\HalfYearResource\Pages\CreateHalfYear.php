<?php

namespace App\Filament\Resources\HalfYearResource\Pages;

use App\Filament\Resources\HalfYearResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateHalf<PERSON>ear extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = HalfYearResource::class;

    public function getTitle(): string
    {
        return __('resource.half_year_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            
            Actions\LocaleSwitcher::make(),
        ];
    }
}
