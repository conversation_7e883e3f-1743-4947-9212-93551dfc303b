<?php

namespace App\Filament\Resources\UnitTypeResource\Pages;

use App\Filament\Resources\UnitTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUnitTypes extends ListRecords
{

    protected static string $resource = UnitTypeResource::class;

    public function getTitle(): string
    {
        return __('resource.unit_type_resource.label');
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\LocaleSwitcher::make(),
            Actions\CreateAction::make(),
        ];
    }
}
