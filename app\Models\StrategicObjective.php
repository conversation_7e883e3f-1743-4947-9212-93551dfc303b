<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class StrategicObjective extends Model
{
    use HasTranslations;

    protected $fillable = [
        'strategic_objective',
        'description',
        'perspective_id',
        'weight',
        'number_of_year',
        'start_date',
        'strategy_actions',
        'end_date',
        'expected_results',
        'is_approved',
        'approved_by',
        'user_id',
        'is_active',
    ];

    protected $casts = [
        'strategic_objective' => 'array',
        'description' => 'array',
    ];

    public $translatable = ['strategic_objective', 'description'];



    public function perspective()
    {
        return $this->belongsTo(Perspective::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
