<?php

namespace App\Filament\Resources\StrategicObjectiveResource\Pages;

use App\Filament\Resources\StrategicObjectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditStrategicObjective extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = StrategicObjectiveResource::class;

    public function getTitle(): string
    {
        return __('resource.strategic_objective_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
