<?php

namespace App\Filament\Resources\JobPositionResource\Pages;

use App\Filament\Resources\JobPositionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateJobPosition extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = JobPositionResource::class;

    public function getTitle(): string
    {
        return __('resource.job_position_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
