<?php

namespace App\Filament\Resources\QuarterResource\Pages;

use App\Filament\Resources\QuarterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditQuarter extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = QuarterResource::class;

    public function getTitle(): string
    {
        return __('resource.quarter_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
