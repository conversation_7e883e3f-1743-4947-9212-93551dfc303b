<?php

namespace App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;

use App\Filament\Resources\StrategicObjectiveAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;


class CreateStrategicObjectiveAssignment extends CreateRecord
{
    protected static string $resource = StrategicObjectiveAssignmentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // This method handles the main create button - creates assignments for all units
        $createdCount = 0;
        $errors = [];

        foreach ($data as $key => $value) {
            if (str_starts_with($key, 'unit_') && str_ends_with($key, '_strategic_objective_id') && !empty($value)) {
                $unitId = str_replace(['unit_', '_strategic_objective_id'], '', $key);

                $strategicObjectiveId = $data["unit_{$unitId}_strategic_objective_id"] ?? null;
                $assignmentWeight = $data["unit_{$unitId}_assignment_weight"] ?? null;
                $isActive = $data["unit_{$unitId}_is_active"] ?? true;
                $organizationUnitId = $data["unit_{$unitId}_organization_unit_id"] ?? $unitId;

                // Skip if required fields are missing
                if (empty($strategicObjectiveId) || empty($assignmentWeight)) {
                    continue;
                }

                // Check if assignment already exists
                $existingAssignment = $this->getModel()::where([
                    'strategic_objective_id' => $strategicObjectiveId,
                    'organization_unit_id' => $organizationUnitId,
                ])->first();

                if ($existingAssignment) {
                    // Update existing assignment
                    $existingAssignment->update([
                        'weight' => $assignmentWeight,
                        'is_active' => $isActive,
                    ]);
                    $createdCount++;
                } else {
                    // Create new assignment
                    try {
                        $this->getModel()::create([
                            'strategic_objective_id' => $strategicObjectiveId,
                            'organization_unit_id' => $organizationUnitId,
                            'weight' => $assignmentWeight,
                            'is_active' => $isActive,
                        ]);
                        $createdCount++;
                    } catch (\Exception $e) {
                        $unitName = \App\Models\OrganizationUnit::find($organizationUnitId)?->name ?? "Unit {$unitId}";
                        $errors[] = "Failed to create assignment for {$unitName}";
                    }
                }
            }
        }

        // Show notifications
        if ($createdCount > 0) {
            \Filament\Notifications\Notification::make()
                ->title(__('resource.strategic_objective_assignment_resource.notifications.bulk_created_success', ['count' => $createdCount]))
                ->success()
                ->send();
        }

        if (!empty($errors)) {
            \Filament\Notifications\Notification::make()
                ->title(__('resource.strategic_objective_assignment_resource.notifications.some_errors'))
                ->body(implode(', ', $errors))
                ->warning()
                ->send();
        }

        if ($createdCount === 0) {
            \Filament\Notifications\Notification::make()
                ->title(__('resource.strategic_objective_assignment_resource.notifications.no_valid_data'))
                ->body(__('resource.strategic_objective_assignment_resource.notifications.no_valid_data_description'))
                ->warning()
                ->send();
        }

        // Prevent default record creation since we handle it manually
        $this->halt();

        return $data; // This line will never be reached, but required for method signature
    }

    protected function getCreateFormAction(): \Filament\Actions\Action
    {
        return \Filament\Actions\Action::make('create')
            ->label(__('resource.strategic_objective_assignment_resource.actions.create_all_assignments'))
            ->action('create')
            ->keyBindings(['mod+s'])
            ->color('primary')
            ->icon('heroicon-o-check-circle')
            ->requiresConfirmation()
            ->modalHeading(__('resource.strategic_objective_assignment_resource.modals.confirm_create_all_title'))
            ->modalDescription(__('resource.strategic_objective_assignment_resource.modals.confirm_create_all_description'))
            ->modalSubmitActionLabel(__('resource.strategic_objective_assignment_resource.actions.confirm_create_all'));
    }

    protected function afterCreate(): void
    {
        // Redirect to index after creation
        $this->redirect($this->getResource()::getUrl('index'));
    }
}
