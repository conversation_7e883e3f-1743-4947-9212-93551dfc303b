<?php

namespace App\Filament\Resources\QuarterResource\Pages;

use App\Filament\Resources\QuarterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListQuarters extends ListRecords
{
    protected static string $resource = QuarterResource::class;

    public function getTitle(): string
    {
        return __('resource.quarter_resource.label');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
