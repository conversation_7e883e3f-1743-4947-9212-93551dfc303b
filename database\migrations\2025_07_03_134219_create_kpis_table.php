<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kpis', function (Blueprint $table) {
            $table->id();
            $table->json('name');
            $table->json('description');
            $table->string('total_target');
            $table->unsignedBigInteger('parent_kpi_id')->nullable();
            $table->foreign('parent_kpi_id')->references('id')->on('kpis')->onDelete('cascade');
            $table->unsignedBigInteger('parent_kpi_node_id')->nullable();
            $table->foreign('parent_kpi_node_id')->references('id')->on('kpis')->onDelete('cascade');
            $table->unsignedBigInteger('organization_unit_id');
            $table->foreign('organization_unit_id')->references('id')->on('organization_units')->onDelete('cascade');
            $table->unsignedBigInteger('strategic_objective_id');
            $table->foreign('strategic_objective_id')->references('id')->on('strategic_objectives')->onDelete('cascade');
            $table->unsignedBigInteger('frequency_type_id');
            $table->foreign('frequency_type_id')->references('id')->on('frequency_types')->onDelete('cascade');
            $table->json('unit');
            $table->decimal('weight', 5, 2)->nullable();
            $table->integer('number_of_year')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->boolean('is_approved')->default(false);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpis');
    }
};
