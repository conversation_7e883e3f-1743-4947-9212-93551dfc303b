<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrategicObjectiveResource\Pages;
use App\Filament\Resources\StrategicObjectiveResource\RelationManagers;
use App\Models\StrategicObjective;
use App\Models\Perspective;
use App\Models\Employee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class StrategicObjectiveResource extends Resource
{
    use Translatable;
    protected static ?string $model = StrategicObjective::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.strategic_objective_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.strategic_objective_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('resource.strategic_objective_resource.sections.basic_info'))
                    ->schema([
                        Forms\Components\TextInput::make('strategic_objective')
                            ->label(__('resource.strategic_objective_resource.fields.strategic_objective'))
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label(__('resource.strategic_objective_resource.fields.description'))
                            ->rows(3)
                            ->maxLength(1000),

                        Forms\Components\Select::make('perspective_id')
                            ->label(__('resource.strategic_objective_resource.fields.perspective'))
                            ->relationship('perspective', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\TextInput::make('weight')
                            ->label(__('resource.strategic_objective_resource.fields.weight'))
                            ->numeric()
                            ->step(0.01)
                            ->minValue(0)
                            ->maxValue(100)
                            ->suffix('%'),
                    ])->columns(2),

                Forms\Components\Section::make(__('resource.strategic_objective_resource.sections.timeline'))
                    ->schema([
                        Forms\Components\TextInput::make('number_of_year')
                            ->label(__('resource.strategic_objective_resource.fields.number_of_year'))
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(10),

                        Forms\Components\DatePicker::make('start_date')
                            ->label(__('resource.strategic_objective_resource.fields.start_date'))
                            ->required(),

                        Forms\Components\DatePicker::make('end_date')
                            ->label(__('resource.strategic_objective_resource.fields.end_date'))
                            ->required()
                            ->after('start_date'),
                    ])->columns(3),

                Forms\Components\Section::make(__('resource.strategic_objective_resource.sections.details'))
                    ->schema([
                        Forms\Components\Textarea::make('strategy_actions')
                            ->label(__('resource.strategic_objective_resource.fields.strategy_actions'))
                            ->rows(4)
                            ->maxLength(2000),

                        Forms\Components\Textarea::make('expected_results')
                            ->label(__('resource.strategic_objective_resource.fields.expected_results'))
                            ->rows(4)
                            ->maxLength(2000),
                    ])->columns(1),

                Forms\Components\Section::make(__('resource.strategic_objective_resource.sections.created_by'))
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label(__('resource.strategic_objective_resource.fields.employee'))
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->default(function () {
                                return Auth::id();
                            })
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Toggle::make('is_approved')
                            ->label(__('resource.strategic_objective_resource.fields.is_approved'))
                            ->default(false),

                        Forms\Components\Toggle::make('is_active')
                            ->label(__('resource.strategic_objective_resource.fields.is_active'))
                            ->default(true),
                    ])->columns(3),

                Forms\Components\Section::make(__('resource.strategic_objective_resource.fields.approved_by'))
                    ->schema([

                        Forms\Components\Select::make('approved_by')
                            ->label(__('resource.strategic_objective_resource.fields.approved_by'))
                            ->relationship('approvedBy', 'name')
                            ->searchable()
                            ->preload()
                            ->visible(fn ($get) => $get('is_approved')),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('resource.strategic_objective_resource.fields.id'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('strategic_objective')
                    ->label(__('resource.strategic_objective_resource.fields.strategic_objective'))
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('perspective.name')
                    ->label(__('resource.strategic_objective_resource.fields.perspective'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label(__('resource.strategic_objective_resource.fields.weight'))
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->label(__('resource.strategic_objective_resource.fields.start_date'))
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label(__('resource.strategic_objective_resource.fields.end_date'))
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('resource.strategic_objective_resource.fields.created_by'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label(__('resource.strategic_objective_resource.fields.is_approved'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('resource.strategic_objective_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('resource.strategic_objective_resource.fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('resource.strategic_objective_resource.fields.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('perspective_id')
                    ->label(__('resource.strategic_objective_resource.fields.perspective'))
                    ->relationship('perspective', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\Filter::make('is_approved')
                    ->label(__('resource.strategic_objective_resource.filters.approved'))
                    ->query(fn (Builder $query): Builder => $query->where('is_approved', true)),

                Tables\Filters\Filter::make('is_active')
                    ->label(__('resource.strategic_objective_resource.filters.active'))
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('start_date')
                            ->label(__('resource.strategic_objective_resource.fields.start_date')),
                        Forms\Components\DatePicker::make('end_date')
                            ->label(__('resource.strategic_objective_resource.fields.end_date')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['start_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('start_date', '>=', $date),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('end_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('approve')
                        ->label(__('resource.strategic_objective_resource.actions.approve'))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_approved' => true]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label(__('resource.strategic_objective_resource.actions.activate'))
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => true]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label(__('resource.strategic_objective_resource.actions.deactivate'))
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => false]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrategicObjectives::route('/'),
            'create' => Pages\CreateStrategicObjective::route('/create'),
            //'view' => Pages\ViewStrategicObjective::route('/{record}'),
            'edit' => Pages\EditStrategicObjective::route('/{record}/edit'),
        ];
    }
}
