<?php

namespace App\Filament\Resources\FrequencyTypeResource\Pages;

use App\Filament\Resources\FrequencyTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFrequencyType extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = FrequencyTypeResource::class;

    public function getTitle(): string
    {
        return __('resource.frequency_type_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
