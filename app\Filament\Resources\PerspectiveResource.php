<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PerspectiveResource\Pages;
use App\Filament\Resources\PerspectiveResource\RelationManagers;
use App\Models\Perspective;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PerspectiveResource extends Resource
{
    use Translatable;
    protected static ?string $model = Perspective::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 0;

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.perspective_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.perspective_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.perspective_resource.fields.name'))->required(),
                Forms\Components\RichEditor::make('description')->label(__('resource.perspective_resource.fields.description'))->required(),
                Forms\Components\TextInput::make('weight')->label(__('resource.perspective_resource.fields.weight'))->required(),
                Forms\Components\Toggle::make('is_active')->label(__('resource.perspective_resource.fields.is_active'))->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.perspective_resource.fields.name')),
                Tables\Columns\TextColumn::make('description')->label(__('resource.perspective_resource.fields.description')),
                Tables\Columns\TextColumn::make('weight')->label(__('resource.perspective_resource.fields.weight')),
                Tables\Columns\IconColumn::make('is_active')->label(__('resource.perspective_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPerspectives::route('/'),
            'create' => Pages\CreatePerspective::route('/create'),
            'edit' => Pages\EditPerspective::route('/{record}/edit'),
        ];
    }
}
