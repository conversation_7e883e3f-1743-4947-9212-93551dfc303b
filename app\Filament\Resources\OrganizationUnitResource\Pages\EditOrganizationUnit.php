<?php

namespace App\Filament\Resources\OrganizationUnitResource\Pages;

use App\Filament\Resources\OrganizationUnitResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOrganizationUnit extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = OrganizationUnitResource::class;
    public function getTitle(): string
    {
        return __('resource.organization_unit_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
