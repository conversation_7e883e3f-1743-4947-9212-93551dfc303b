<?php

namespace App\Filament\Resources\PerspectiveResource\Pages;

use App\Filament\Resources\PerspectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPerspective extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = PerspectiveResource::class;

    public function getTitle(): string
    {
        return __('resource.perspective_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
