<?php

namespace App\Filament\Resources\PerspectiveResource\Pages;

use App\Filament\Resources\PerspectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePerspective extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = PerspectiveResource::class;

    public function getTitle(): string
    {
        return __('resource.perspective_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
