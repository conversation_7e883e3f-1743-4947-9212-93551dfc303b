<?php

namespace App\Filament\Resources\QuarterResource\Pages;

use App\Filament\Resources\QuarterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateQuarter extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = QuarterResource::class;

    public function getTitle(): string
    {
        return __('resource.quarter_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
