<?php

namespace App\Filament\Resources\JobTypeResource\Pages;

use App\Filament\Resources\JobTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateJobType extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = JobTypeResource::class;
    public function getTitle(): string
    {
        return __('resource.job_type_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
