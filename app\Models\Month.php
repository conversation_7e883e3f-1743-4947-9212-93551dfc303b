<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Month extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'half_year_id',
        'quarter_id',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'name' => 'array',
    ];

    public $translatable = ['name'];

    public function halfYear()
    {
        return $this->belongsTo(HalfYear::class);
    }

    public function quarter()
    {
        return $this->belongsTo(Quarter::class);
    }
}
