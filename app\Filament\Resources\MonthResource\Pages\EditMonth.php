<?php

namespace App\Filament\Resources\MonthResource\Pages;

use App\Filament\Resources\MonthResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMonth extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = MonthResource::class;

    public function getTitle(): string
    {
        return __('resource.month_resource.edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
