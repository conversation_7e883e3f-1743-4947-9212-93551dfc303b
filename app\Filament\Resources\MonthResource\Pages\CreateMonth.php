<?php

namespace App\Filament\Resources\MonthResource\Pages;

use App\Filament\Resources\MonthResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMonth extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = MonthResource::class;

    public function getTitle(): string
    {
        return __('resource.month_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
