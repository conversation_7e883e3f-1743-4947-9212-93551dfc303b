<?php

namespace App\Filament\Resources\FrequencyTypeResource\Pages;

use App\Filament\Resources\FrequencyTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFrequencyTypes extends ListRecords
{
    protected static string $resource = FrequencyTypeResource::class;

    public function getTitle(): string
    {
        return __('resource.frequency_type_resource.label');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
