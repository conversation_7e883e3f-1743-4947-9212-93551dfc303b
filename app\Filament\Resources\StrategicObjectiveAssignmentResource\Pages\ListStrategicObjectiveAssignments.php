<?php

namespace App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;

use App\Filament\Resources\StrategicObjectiveAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListStrategicObjectiveAssignments extends ListRecords
{
    protected static string $resource = StrategicObjectiveAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
