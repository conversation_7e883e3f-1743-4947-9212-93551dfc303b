<?php

namespace App\Filament\Resources\JobTitleCategoryResource\Pages;

use App\Filament\Resources\JobTitleCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateJobTitleCategory extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = JobTitleCategoryResource::class;
    public function getTitle(): string
    {
        return __('resource.job_title_category_resource.create');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

}
